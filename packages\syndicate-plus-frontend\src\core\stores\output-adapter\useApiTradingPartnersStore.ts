import { defineStore } from 'pinia';
import { ref } from 'vue';
import { ApiTradingPartner, ApiTradingPartnerCategory } from '@core/interfaces/ApiTradingPartner';
import { fetchTradingPartnersFromApi, fetchTradingPartnerCategoriesApi } from '@core/services/TradingPartners';
import isFeatureEnabled from '@utils/isFeatureEnabled';
import { useEnabledApiStore } from '@core/stores/output-adapter/useEnabledApiStore';

export const useApiTradingPartnersStore = defineStore('coreApiTradingPartners', () => {
  // Refs
  const isLoading = ref<boolean>(false);
  const tradingPartners = ref<ApiTradingPartner[]>([]);
  const tradingPartnerCategories = ref<ApiTradingPartnerCategory[]>([]);
  const dictionary = ref<Record<string, ApiTradingPartnerCategory[]>>({});

  // Functions
  const fetch = async (): Promise<void> => {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;
    try {
      let sortedTradingPartners = (await fetchTradingPartnersFromApi()).sort((a, b) => a.name.localeCompare(b.name));

      if (!isFeatureEnabled('not-ready')) {
        const enabledApiStore = useEnabledApiStore();

        await enabledApiStore.fetchEnabledApis();
        const enabledApiIds = enabledApiStore.enabledApis;
        sortedTradingPartners = sortedTradingPartners.filter((api) => enabledApiIds.includes(api.id));
      }

      tradingPartners.value = sortedTradingPartners;
    } catch (e) {
      console.error('Error fetching trading partners', e);
    } finally {
      isLoading.value = false;
    }
  };

  const fetchCategories = async (tradingPartnerId: string): Promise<void> => {
    if (isLoading.value) {
      return;
    }

    if (dictionary.value[tradingPartnerId]) {
      tradingPartnerCategories.value = dictionary.value[tradingPartnerId];
      return;
    }

    try {
      isLoading.value = true;
      tradingPartnerCategories.value = (await fetchTradingPartnerCategoriesApi(tradingPartnerId)).sort((a, b) =>
        a.name.localeCompare(b.name)
      );
      dictionary.value[tradingPartnerId] = tradingPartnerCategories.value;
    } catch (e) {
      console.error('Error fetching trading partner categories', e);
    } finally {
      isLoading.value = false;
    }
  };

  return {
    isLoading,
    tradingPartners,
    tradingPartnerCategories,
    fetch,
    fetchCategories,
  };
});
