type FeatureNames = {
  'aplus-old': string;
  'aplus-products-bar': string;
  'inbound-tab': string;
  'internal-names': string;
  'aplus-fields': string;
  'cancel-syndication': string;
  skus: string;
  'below-the-fold': string;
  'price-import': string;
  'syndicate-core': string;
  'disable-partners': string;
  '3rd-party-messaging': string;
  'reference-tables': string;
  'not-ready': string;
  'all-apis': string;
  'core-below-the-fold': string;
  'plus-v2-enabled': string;
  'syndicate-advance': string;
  'dsa-mapping': string;
  'amazon-auth': string;
};

function getQueryParam(name) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
}

function isFeatureEnabled<T extends keyof FeatureNames>(featureName: T) {
  try {
    return (
      getQueryParam(featureName) === 'true' ||
      localStorage.getItem(`feature.${featureName}`) === 'true' ||
      import.meta.env[`VITE_FEATURE_${featureName.toUpperCase()}`] === 'true'
    );
  } catch (e) {
    console.error(e);
    return false;
  }
}

export default isFeatureEnabled;
