<template>
  <c-section>
    <!-- Only show content when fully initialized -->
    <template v-if="isInitialized">
      <!-- Show content when initialized -->
      <teleport v-if="isTeleportEnabled" to="#right-sidebar">
        <c-tile-btn
          v-if="isEditButtonVisible"
          icon="mdi-pencil-outline"
          :icon-size="20"
          :tooltip-left="$t('syndicate_plus.common.edit')"
          @click="goToEditPage"
        />
      </teleport>

      <div class="filter-panel">
        <c-select
          v-model="selectedMapping"
          :options="allMappings"
          :placeholder="$t('syndicate_plus.common.mapping')"
          :label="$t('syndicate_plus.common.mapping')"
          option-label="MappingName"
          option-value="MappingId"
          @update:model-value="handleMappingSelection"
        />
      </div>

      <q-inner-loading v-if="isLoading" :showing="isLoading" color="primary" class="z-10 inner-loading">
        <c-spinner size="40" class="spinner" />
      </q-inner-loading>

      <div v-else-if="selectedMapping">
        <q-table
          :rows="selectedMappingFields || []"
          :columns="columns"
          :pagination="{
            page: 1,
            rowsPerPage: 0,
          }"
          row-key="targetFieldName"
          separator="cell"
          dense
          :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
          flat
          class="settings-field-mapping-table hide-checkboxes"
          hide-bottom
        >
          <!-- ...existing template slots... -->
          <template #body-cell-status="props">
            <q-td>
              <c-small-square :color="props.row.mapped ? SquareColor.GREEN : SquareColor.RED">
                <q-tooltip>
                  {{
                    props.row.mapped
                      ? $t('syndicate_plus.mapping.mapped_field')
                      : $t('syndicate_plus.mapping.unmapped_field')
                  }}
                </q-tooltip>
              </c-small-square>
            </q-td>
          </template>
          <template #body-cell-actions>
            <c-table-actions>
              <q-icon name="mdi-arrow-right" size="xs" class="grey-dark" />
            </c-table-actions>
          </template>
          <template #body-cell-function="props">
            <q-td :props="props">
              <c-field-mapping-function :converter-args="props.row.function" />
            </q-td>
          </template>
          <template #body-cell-importanceType="props">
            <q-td :props="props">
              <div class="flex justify-start">
                {{ $t(`syndicate_plus.mapping.importace_type.${props.row.importanceType}`) }}
              </div>
            </q-td>
          </template>
          <template #body-cell-dataType="props">
            <q-td :props="props">
              {{ props.row.dataType?.toLowerCase() }}
            </q-td>
          </template>
          <template #body-cell-length="props">
            <q-td :props="props">
              <c-mapping-length :min-length="props.row.minLength" :max-length="props.row.maxLength" />
            </q-td>
          </template>
        </q-table>
      </div>
      <div v-else-if="!isLoading && hasInitialized && isInitialized && allMappings.length === 0">
        <c-no-data
          src="nothing-to-see"
          image-height="195px"
          :title="$t('syndicate_plus.mapping.media_mapping_tab.no_data.no_mappings_title')"
          :text="$t('syndicate_plus.mapping.media_mapping_tab.no_data.no_mappings_message')"
        />
      </div>
    </template>
  </c-section>
</template>

<script setup lang="ts">
import { onBeforeMount, computed, onUnmounted, ref, onMounted } from 'vue';
import { CMappingLength } from '@components/FieldMapping';
import { CNoData, CSmallSquare } from '@components';
import { CFieldMappingFunction } from '@core/components/FieldMapping';
import { SquareColor } from '@enums';
import { useRouter } from '@composables';
import { isSyndicateAdvanceConfigEnabled, isSyndicatePlusWithCoreEnabled } from '@utils/moduleEnabledService';
import { useFieldMappingTabPanel } from '@core/composables/useFieldMappingTabPanel';
import { useShowIdState } from '@core/composables/FieldMapping';

const props = defineProps({
  tradingPartnerName: {
    type: String,
    required: true,
  },
});

const { isId } = useShowIdState();

// Use the composable
const {
  isLoading,
  hasInitialized,
  isInitialized,
  allMappings,
  selectedMapping,
  selectedMappingFields,
  isDynamicMappingSelected,
  currentTradingPartnerId,
  selectedDynamicMappingFormatId,
  handleMappingSelection,
  initializeStore,
  clearStore,
} = useFieldMappingTabPanel(props.tradingPartnerName);

// Expose isInitialized to parent component
defineExpose({
  isInitialized,
});

// Local refs
const isTeleportEnabled = ref(false);

// Computed properties
const isEditButtonVisible = computed(() => {
  if (!selectedMapping.value) {
    return false;
  }
  if (!(isSyndicatePlusWithCoreEnabled() || isSyndicateAdvanceConfigEnabled())) {
    return false;
  }

  // Allow edit for all mappings, including dynamic mappings
  return true;
});

const columns = computed(() => {
  return [
    {
      name: 'status',
      field: 'status',
      label: '',
      align: 'left' as const,
      style: 'width: 5%',
    },
    {
      name: isId ? 'sourceFieldId' : 'sourceFieldDisplayName',
      field: isId ? 'sourceFieldId' : 'sourceFieldDisplayName',
      label: 'source field',
      align: 'left' as const,
      style: 'width: 5%',
      featureFlag: true,
    },
    {
      name: 'actions',
      field: 'actions',
      label: '',
      align: 'center' as const,
    },
    {
      name: 'tradingPartnerField',
      field: 'tradingPartnerField',
      label: `${props.tradingPartnerName?.toLowerCase()} field`,
      align: 'left' as const,
      style: 'width: 20%; color: var(--color-grey-dark)',
    },
    {
      name: 'defaultValue',
      field: 'defaultValue',
      label: 'default value',
      align: 'left' as const,
      style: 'width: 13%',
    },
    {
      name: 'function',
      field: 'function',
      label: 'function',
      align: 'left' as const,
      style: 'width: 20%',
    },
    {
      name: 'importanceType',
      field: 'importanceType',
      label: 'importance',
      align: 'left' as const,
      style: 'width: 5%; color: var(--color-grey-dark)',
    },
    {
      name: 'FormatDataType',
      field: 'FormatDataType',
      label: `data type`,
      align: 'left' as const,
      style: 'width: 5%',
    },
    {
      name: 'length',
      field: 'length',
      label: `length`,
      align: 'left' as const,
      style: 'width: 8%',
    },
  ];
});

// Composables
const { goToPage } = useRouter();

// Functions
const goToEditPage = () => {
  if (isDynamicMappingSelected.value && selectedDynamicMappingFormatId.value) {
    // For dynamic mappings, use the new route with proper parent
    goToPage('edit-dynamic-mapping-from-tab-page', {
      formatId: selectedDynamicMappingFormatId.value,
      mappingId: selectedMapping.value?.MappingId,
      tradingPartnerId: currentTradingPartnerId.value,
    });
  } else {
    // For regular mappings, use the existing edit-field-mapping-page route
    goToPage('edit-field-mapping-page', {
      mappingId: selectedMapping.value?.MappingId,
    });
  }
};

// Lifecycle methods
// When the component mounts, we want to ensure loading state is shown first
onBeforeMount(async () => {
  // Initialize the store which will show loading state
  await initializeStore();
});

onMounted(() => {
  isTeleportEnabled.value = true;
});

onUnmounted(() => clearStore());
</script>

<style lang="scss" scoped>
.settings-field-mapping-table {
  max-height: calc(100vh - 210px);
  margin-bottom: 200px;
}

.spinner {
  margin: auto;
  width: min-content;
}

.filter-panel {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.c-field-mapping-function {
  font-size: 10px;
  padding: 0px;

  .q-card {
    border-radius: 30px;
    box-shadow: none;
  }
}

.c-field-mapping-function .q-card > div:last-child,
.q-card > img:last-child {
  border-radius: 30px;
  border: 1.5px solid var(--color-grey-dark);
  padding: 4px;
  box-shadow: none;
}

.color-card,
.color-custom-card {
  background-color: var(--color-primary);
  height: 20px;
  line-height: 11px;

  &.function-name {
    background-color: var(--color-grey-lighter);
  }
}

:deep(.c-inri-input.q-field--with-bottom) {
  padding-bottom: 20px;
}
</style>
