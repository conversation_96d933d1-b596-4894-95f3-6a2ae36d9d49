import { OutputType, DataLevel, DeliveryMethod, ExcelOutputMode } from '@core/enums';

export interface FormatOutputs {
  formatId: number;
  formatName: string;
  outputs: OutputBaseLink[];
}

export interface OutputBaseLink {
  extensionId: string;
  extensionName: string;
  outputFormat: string;
}

export interface CoreOutputLinkResponse extends OutputBaseLink {
  coreFormatId: number;
}

export interface DynamicOutputLinkResponse extends OutputBaseLink {
  environmentFormatId: number;
}

export interface OutputResponse {
  ExtensionId: string;
  DisplayName: string;
  Settings: OutputExcelSettings | OutputSettings | OutputCSVSettings;
}

export interface NewOutput {
  OutputName: string;
  OutputType: OutputType;
  Settings: OutputExcelSettings | OutputSettings | OutputCSVSettings;
}

export interface OutputSettings
  extends SFTPDeliverySettings,
    HTTPPOSTDeliverySettings,
    FTPSDeliverySettings,
    FTPDeliverySettings,
    AzureBlobDeliverySettings {
  EnableCompression: boolean;
  OutputFormat: string;
  DeliveryMethods: string;
  DeliveryMethodsList?: DeliveryMethod[];
}

export interface OutputExcelSettings extends OutputSettings {
  Mode: ExcelOutputMode;
  ExportExcelStartRow: number;
  ExportExcelStartColumn: number;
  TemplateFilename: string;
  ExportFilename: string;
  ExportExcelWorksheetsName: string;
  MultiTabWorksheetStartCells: string;
  UseZip: boolean;
  UseUTF8: boolean;
}

export interface OutputCSVSettings extends OutputSettings {
  Delimiter: string;
  DataLevel: DataLevel;
}

export interface SFTPDeliverySettings {
  SFTP_FileName?: string;
  SFTP_Host?: string;
  SFTP_UserName?: string;
  SFTP_Password?: string;
  SFTP_Path?: string;
  SFTP_Port?: string;
}

export interface HTTPPOSTDeliverySettings {
  Http_Filename?: string;
  Http_Url?: string;
}

export interface FTPSDeliverySettings {
  FTPS_FileName?: string;
  FTPS_Host?: string;
  FTPS_UserName?: string;
  FTPS_Password?: string;
  FTPS_Path?: string;
  FTPS_Port?: string;
}
export interface FTPDeliverySettings {
  FTP_FileName?: string;
  FTP_Host?: string;
  FTP_UserName?: string;
  FTP_Password?: string;
  FTP_Path?: string;
  FTP_Port?: string;
}

export interface AzureBlobDeliverySettings {
  Azure_FileName?: string;
  Azure_ConnectionString?: string;
  Azure_Container?: string;
  Azure_Path?: string;
}

export interface WorksheetPair {
  worksheetName: string;
  startCell: string;
}
