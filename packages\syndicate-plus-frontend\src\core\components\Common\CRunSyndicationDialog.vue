<template>
  <c-dialog
    v-model="showDialog"
    class="c-dialog"
    :title="dialogTitle"
    :confirm-button-text="confirmButtonText"
    :is-loading="isLoading"
    :disable-confirm="isInvalidForm"
    :disabled-confirm-tooltip="disableConfirmTooltip"
    @confirm="handleConfirm"
    @cancel="cancel"
  >
    <div class="w-1/2">
      <c-select
        v-model="selectedMapping"
        :options="mappingsForSelectedCollection"
        :label="$t('core.trading_partners.collections.select_mapping')"
        :placeholder="$t('core.trading_partners.collections.select_mapping')"
        option-label="text"
        option-value="id"
        clearable
        hide-bottom-space
        @update:model-value="onChangeMapping"
      />
      <c-select
        v-if="showOutputSelect"
        v-model="selectedOutput"
        :options="outputs"
        :label="$t('core.trading_partners.collections.select_output')"
        :placeholder="$t('core.trading_partners.collections.select_output')"
        :option-label="getOptionLabel"
        option-value="ExtensionId"
        hide-bottom-space
        clearable
      />
      <q-checkbox
        v-if="isScheduledCheckboxVisible"
        v-model="isScheduled"
        v-bind="$inri.checkbox"
        :label="$t('core.trading_partners.scheduled_syndication.scheduled')"
        class="pb-2"
      />
      <c-scheduled-run-configuration v-if="isScheduled" ref="scheduledRunConfigurationRef" />
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { onBeforeMount, toRef, PropType, watchEffect, ref, computed } from 'vue';
import { useDialog, notify } from '@inriver/inri';
import { useRunSyndicationDialog } from '@core/composables';
import { useI18n } from 'vue-i18n';
import { useCoreTradingPartnersStore, useCurrentCoreTradingPartnerStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { useRouter } from '@composables/useRouter';
import { ChannelLinkResponse, DynamicMappingResponse, Mapping, Output } from '@core/interfaces';
import { Collection, Workarea } from '@core/interfaces/Workarea';
import { DynamicOutputType, ResponseStatus } from '@core/enums';
import { fetchDynamicAssignedOutputs } from '@core/services/Outputs/assignedDynamicOutputsApi';
import { CoreOutputLinkResponse, DynamicOutputLinkResponse } from '@core/interfaces/Outputs';
import { fetchCoreAssignedOutputs } from '@core/services/Outputs';
import { CScheduledRunConfiguration } from '@core/components/Common';
import { Frequency } from '@enums/ApiSyndication';

const props = defineProps({
  selectedCollection: {
    type: Object as PropType<Collection<ChannelLinkResponse | Workarea>>,
    required: true,
  },
  selectedEntityIds: {
    type: Array<number>,
    required: false,
    default: null,
  },
  runReviewDialog: {
    type: Boolean,
    required: false,
    default: false,
  },
  runDsaDialog: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const { goToPage, route } = useRouter();

// Variables
const tradingPartnerId = route.params.tradingPartnerId as string;

// Stores
const currentCoreTradingPartnerStore = useCurrentCoreTradingPartnerStore();
const tradingPartnersStore = useCoreTradingPartnersStore();

// Refs
const isReviewDialog = toRef(props, 'runReviewDialog');
const isDsaDialog = toRef(props, 'runDsaDialog');

const dynamicAssignedOutputs = ref<DynamicOutputLinkResponse[]>([]);
const coreAssignedOutputs = ref<CoreOutputLinkResponse[]>([]);
const isScheduled = ref<boolean>(false);
const scheduledRunConfigurationRef = ref<InstanceType<typeof CScheduledRunConfiguration> | null>(null);

// Computed
const dialogTitle = computed(() => {
  if (isReviewDialog.value) {
    return t('core.review.title');
  }

  return isDsaDialog.value ? t('core.trading_partners.send_to_dsa') : t('core.trading_partners.syndicate');
});
const confirmButtonText = computed(() => {
  if (isDsaDialog.value) {
    return t('core.trading_partners.send_to_dsa');
  }

  return isReviewDialog.value ? t('core.review.title') : t('syndicate_plus.common.save');
});
const isInvalidForm = computed(() =>
  isScheduled.value ? scheduledRunConfigurationRef.value?.isInvalid ?? false : confirmIsDisabled.value
);
const showOutputSelect = computed(() => !isDsaDialog.value && !isReviewDialog.value);
const disableConfirmTooltip = computed(() => {
  const scheduleName = scheduledRunConfigurationRef.value?.scheduleName || '';
  const selectedFrequency = scheduledRunConfigurationRef.value?.selectedFrequency || null;
  if (
    isInvalidForm.value &&
    scheduleName.length &&
    (selectedFrequency === Frequency.ONCE || selectedFrequency === Frequency.MONTHLY)
  ) {
    return t('core.trading_partners.scheduled_syndication.schedule_validation_error');
  }

  return '';
});

// Composables
const { t } = useI18n();
const { showDialog, cancel, confirmSuccess } = useDialog();
const { currentSyndications, currentDynamicMappings, currentMappings } = storeToRefs(currentCoreTradingPartnerStore);
const { coreFormats, dynamicFormats } = storeToRefs(tradingPartnersStore);
const {
  isLoading,
  confirmIsDisabled,
  selectedMapping,
  mappingsForSelectedCollection,
  outputs,
  selectedOutput,
  init,
  onChangeMapping,
  onConfirm,
  onConfirmScheduledRun,
} = useRunSyndicationDialog(
  currentSyndications,
  currentMappings,
  currentDynamicMappings,
  coreFormats,
  dynamicFormats,
  props.selectedCollection,
  dynamicAssignedOutputs,
  coreAssignedOutputs,
  isReviewDialog,
  isDsaDialog
);
const isScheduledCheckboxVisible = computed(
  () => !isReviewDialog.value && !isDsaDialog.value && !props.selectedEntityIds
);

// Functions
const getOptionLabel = (output: Output) =>
  output.OutputFormat
    ? `${output.ExtensionDisplayName} (${output.OutputFormat?.toLocaleLowerCase()})`
    : `${output.ExtensionDisplayName}`;

const handleConfirm = async (): Promise<void> => {
  if (isDsaDialog.value) {
    await confirm();
    return;
  }

  isReviewDialog.value ? await runReview() : await confirm();
};

const confirm = async () => {
  if (isScheduled.value) {
    await confirmSheduledSyndication();
  } else {
    const jobResult = await onConfirm(props.selectedEntityIds);
    if (jobResult?.status === ResponseStatus.Success) {
      confirmSuccess(t('core.trading_partners.history.syndication_started'));
    } else if (jobResult?.status === ResponseStatus.Unavailable) {
      notify.error(t('core.trading_partners.history.syndication_unavailable'));
    } else {
      notify.error(t('core.trading_partners.history.syndication_error'));
    }
  }
};

const confirmSheduledSyndication = async () => {
  const scheduledRunConfiguration = isScheduled.value
    ? scheduledRunConfigurationRef.value?.getConfiguration()
    : undefined;
  if (!scheduledRunConfiguration) {
    console.error('scheduledRunConfiguration can not be undefined');
    notify.error(t('core.trading_partners.scheduled_syndication.schedule_error'));
    return;
  }

  const result = await onConfirmScheduledRun(scheduledRunConfiguration, tradingPartnerId, props.selectedEntityIds);
  if (!result?.Success) {
    notify.error(t('core.trading_partners.scheduled_syndication.schedule_error'));
    return;
  }

  confirmSuccess(t('core.trading_partners.scheduled_syndication.schedule_success'));
};

const runReview = async () => {
  const isReview = true;
  const jobResult = await onConfirm(props.selectedEntityIds, isReview);
  if (jobResult?.status === ResponseStatus.Success) {
    await confirmSuccess(null);
    const isDynamic = !!(selectedMapping.value?.metadata as DynamicMappingResponse)?.id;
    const mappingId = isDynamic
      ? (selectedMapping.value?.metadata as DynamicMappingResponse)?.id
      : (selectedMapping.value?.metadata as Mapping).MappingId;
    const name = props.selectedCollection.text;
    goToPage(
      'review',
      {
        jobId: jobResult.jobId,
        mappingId,
        collectionName: name ?? `${t('core.review.custom_selection')}`,
      },
      {
        isDynamic,
      }
    );
  } else {
    notify.error(t('core.review.review_error'));
  }
};

// Lifecycle methods
onBeforeMount(async () => {
  isLoading.value = true;
  try {
    dynamicAssignedOutputs.value = await fetchDynamicAssignedOutputs(tradingPartnerId);
    coreAssignedOutputs.value = await fetchCoreAssignedOutputs(tradingPartnerId);
  } finally {
    isLoading.value = false;
  }

  init();
});

watchEffect(() => {
  if (mappingsForSelectedCollection.value?.length && !selectedMapping.value) {
    selectedMapping.value = mappingsForSelectedCollection.value[0];
  }
});

watchEffect(() => {
  if (outputs.value?.length && !selectedOutput.value) {
    selectedOutput.value = outputs.value[0] as Output | DynamicOutputType;
  }
});
</script>
