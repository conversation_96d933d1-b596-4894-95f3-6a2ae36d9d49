<template>
  <section>
    <c-mapping-buttons :config="buttonConfig" :is-visible="props.isVisible" />
    <teleport v-if="isTeleportEnabled" to="#main-section">
      <c-confirm-dialog
        v-if="showDialog"
        v-model:show="showDialog"
        :title="$t('core.replace_mapped_source_field_dialog.title')"
        :text="$t('core.replace_mapped_source_field_dialog.text')"
        :confirm-button-text="$t('core.replace_mapped_source_field_dialog.confirm_button_text')"
        @handle-confirm="confirm"
        @handle-cancel="cancel"
      />
      <c-confirm-dialog
        v-if="showConfirmDialog"
        v-model:show="showConfirmDialog"
        :title="$t('core.cancel_edit_mapping_dialog.title')"
        :text="$t('core.cancel_edit_mapping_dialog.text')"
        :confirm-button-text="$t('core.cancel_edit_mapping_dialog.confirm_button_text')"
        @handle-confirm="handleConfirm"
        @handle-cancel="handleCancel"
      />
      <c-default-language-dialog
        v-if="showDefaultLanguageDialog"
        @set-default-language="(language, applyToAll) => setDefaultLanguage(language, applyToAll)"
        @cancel="closeDefaultLanguageDialog"
      />
    </teleport>

    <div v-if="mapping">
      <q-file
        ref="fileUploader"
        data-id="mapping-file-uploader"
        :model-value="uploadMappingModel"
        accept=".json"
        style="display: none"
        @update:model-value="(file) => handleFiles([file])"
      />
      <div class="flex flex-nowrap">
        <c-select
          v-model="selectedCategory"
          :options="allCategories"
          :label="$t('core.settings.field_mapping.category')"
          class="w-1/4 pr-10px"
          clearable
          hide-bottom-space
        />
        <c-select
          v-model="selectedFieldTypes"
          :options="allFieldTypes"
          :label="$t('core.settings.field_mapping.field_type')"
          class="w-1/4 pr-10px"
          clearable
          multiple
          hide-bottom-space
        />
        <c-select
          v-model="selectedMapState"
          :options="allMapStates"
          :label="$t('core.settings.field_mapping.state')"
          class="w-1/4 pr-10px"
          clearable
          hide-bottom-space
        />
        <c-inri-search
          v-model="searchValue"
          class="c-inri-custom-search"
          dense
          :placeholder="$t('syndicate_plus.common.filter.search')"
        />
      </div>
      <div class="editor">
        <q-checkbox
          v-model="isId"
          v-bind="$inri.checkbox"
          :label="$t('core.settings.field_mapping.show_id')"
          class="pb-2"
        />
        <c-separated-expansion-item :label="$t('core.settings.field_mapping.fields')" is-expanded>
          <q-table
            v-model:selected="selectedRows"
            flat
            dense
            hide-bottom
            class="edit-field-mapping-table sticky-table-header"
            :pagination="{
              page: 1,
              rowsPerPage: 0,
            }"
            :rows="displayedRows"
            :row-key="(x) => x.FormatFieldId || x.FormatField"
            :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
            :columns="editFieldMappingColumns"
          >
            <template #body="props">
              <q-tr
                :props="props"
                :class="{
                  mapped: !(props.row.Mandatory && !props.row.inRiverFieldTypeId),
                  unmapped: isMandatoryRowMissingMapping(props.row),
                  focused: props.rowIndex === targetIndex,
                  changed:
                    !selectedRows?.some(filterMappingModelList(props.row)) &&
                    editFieldMappingStore.checkIfRowHasChanges(props.row, getActualMappingIndex(props.rowIndex)),
                }"
                style="position: relative"
                class="cursor-pointer"
                @click="(e) => onItemClick(e, props.row)"
              >
                <draggable
                  v-model="dropzonePlaceholder"
                  group="fieldMapping"
                  item-key="FormatField"
                  ghost-class="field-ghost"
                  class="dropzone"
                  :data-index="props.rowIndex"
                  @change="changeMappingRows"
                >
                  <template #item="{ element }">
                    <div>{{ element }}</div>
                  </template>
                </draggable>
                <q-td key="inRiverFieldTypeId" :props="props" class="source">
                  {{
                    isId || props.row.inRiverDataType === DataType.RESOURCE
                      ? props.row.inRiverFieldTypeId
                      : editFieldMappingStore.getFieldDisplayName(
                          props.row.inRiverEntityTypeId,
                          props.row.inRiverFieldTypeId
                        )
                  }}
                </q-td>
                <q-td key="inRiverDataType" :props="props" class="source">
                  {{ props.row.inRiverDataType }}
                </q-td>
                <q-td key="actions" :props="props">
                  <q-icon name="mdi-arrow-right" size="xs" class="grey-dark" />
                </q-td>
                <q-td key="FormatField" :props="props" class="target">
                  {{ props.row.FormatField }}
                </q-td>
                <q-td key="FormatDataType" :props="props" class="target">
                  <div style="z-index: 3; position: relative">
                    {{
                      props.row.UnitType
                        ? `${props.row.FormatDataType} (${props.row.UnitType})`
                        : props.row.FormatDataType
                    }}
                    <q-tooltip v-if="isEnumField(props.row)">
                      {{ `${props.row.Enumerations.map((x) => x.EnumValue).join(', ')}` }}
                    </q-tooltip>
                  </div>
                </q-td>
                <q-td key="function" :props="props">
                  <c-field-mapping-function :converter-args="props.row.ConverterArgs" />
                </q-td>
                <q-td key="Description" :props="props">
                  <!-- TODO: Remove z-index (hide/show draggable) -->
                  <q-icon v-if="!!props.row.Description" name="mdi-information" size="xs" style="z-index: 3">
                    <q-tooltip>
                      {{ props.row.Description }}
                    </q-tooltip>
                  </q-icon>
                </q-td>
                <q-td key="Mandatory" :props="props">
                  <div v-if="props.row.Mandatory" class="square">
                    <c-small-square :color="SquareColor.RED">
                      <q-tooltip>mandatory</q-tooltip>
                    </c-small-square>
                  </div>
                </q-td>
                <q-td key="Recommended" :props="props">
                  <div v-if="props.row.Recommended" class="square">
                    <c-small-square :color="SquareColor.PURPLE">
                      <q-tooltip>recommended</q-tooltip>
                    </c-small-square>
                  </div>
                </q-td>
                <q-td key="Unique" :props="props">
                  <div v-if="props.row.Unique" class="square">
                    <c-small-square v-if="props.row.Unique" :color="SquareColor.ORANGE">
                      <q-tooltip>unique</q-tooltip>
                    </c-small-square>
                  </div>
                </q-td>
              </q-tr>
            </template>
          </q-table>
        </c-separated-expansion-item>
        <c-separated-expansion-item
          v-if="!isDynamicMapping"
          :label="$t('core.settings.field_mapping.resource_export')"
          is-expanded
        >
          <div class="edit-field-mapping-resources">
            <draggable
              v-if="isResourceDropzoneVisible"
              v-model="dropzonePlaceholder"
              :group="{
                name: 'fieldMapping',
              }"
              item-key="FormatField"
              ghost-class="field-ghost"
              class="resource-dropzone"
              :class="{ 'resource-focused': isResourceDropzoneFocused }"
              @change="changeResourceMappingRows"
            >
              <template #item="{ element }">
                <div>{{ element }}</div>
              </template>
            </draggable>
            <q-table
              v-model:selected="selectedResourceRows"
              :rows="(mapping as MappingDetailsResponse).ResourceFields"
              :columns="editFieldMappingResourcesColumns"
              flat
              dense
              hide-bottom
              class="sticky-table-header"
              :pagination="{
                page: 1,
                rowsPerPage: 0,
              }"
              row-key="Id"
              :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
              @row-click="onResourceRowClick"
            >
              <template #body-cell-function="props">
                <q-td>
                  <c-field-mapping-function :converter-args="props.row.ConverterArgs" />
                </q-td>
              </template>
            </q-table>
          </div>
        </c-separated-expansion-item>
        <c-base-function-configuration-dialog
          v-if="showFunctionDialog && dialogRowModel && (!targetPanel || targetPanel === 'standard')"
          :row-model="dialogRowModel"
          :function-model="(draggedElement as FunctionModel)"
          :is-new-function="(draggedElement as FunctionModel) !== undefined"
          @save-settings="saveSettings"
          @cancel="cancel"
        />
      </div>
      <c-map-enum-dialog
        v-if="showMapEnumDialog"
        v-model:show="showMapEnumDialog"
        :row-model="(selectedRows[0] as MappingFieldResponse)"
      />
    </div>
  </section>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { v4 as uuidv4 } from 'uuid';
import draggable from 'vuedraggable';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { useRouter } from '@composables/useRouter';
import { useEditFieldMappingDragStore, useEditFieldMappingStore } from '@core/stores';
import {
  DynamicMappingFieldResponse,
  FunctionModel,
  InriverFieldType,
  MappingDetailsResponse,
  MappingFieldResponse,
  MappingResourceFieldResponse,
} from '@core/interfaces';
import { CConfirmDialog, CInriSearch, CSmallSquare } from '@components';
import { SquareColor } from '@enums';
import { RowTargetType, DataType } from '@core/enums';
import {
  CDefaultLanguageDialog,
  CFieldMappingFunction,
  CMappingButtons,
  CMapEnumDialog,
} from '@core/components/FieldMapping';
import { useLazySelectable } from '@composables';
import { useConfirmDialog } from '@composables/Common';
import { useEditFieldMappingFilter } from '@core/composables';
import { useDefaultLanguageDialog, useMapEnumDialogButtons } from '@core/composables/FieldMapping';
import { useFieldMappingButtons, MappingPanelType } from '@core/composables/FieldMapping/useFieldMappingButtons';
import { editFieldMappingColumns, editFieldMappingResourcesColumns } from '@core/const';
import { CSeparatedExpansionItem } from '@components/Shared';
import { CBaseFunctionConfigurationDialog } from '@core/components/FieldMapping/Functions';
import { ConverterTransformation } from '@core/interfaces/FieldMapping/Functions';
import { checkIfFunctionRequiresSourceField } from '@core/Utils';
import { useShowIdState } from '@core/composables/FieldMapping/useShowIdState';
import {
  copyTemplateWithOverrideMappings,
  exportMapping,
  getCleanedUpMappingFields,
  importMapping,
} from '@core/services/Mappings/exportImportMappingService';
import { isMandatoryRowMissingMapping } from '@core/components/FieldMapping/isMandatoryRowMissingMapping';

// Variables
const emit = defineEmits(['cancel-edit']);
const props = defineProps({
  mappingId: {
    type: Number,
    required: true,
  },
  isVisible: {
    type: Boolean,
    default: true,
  },
  saveHandler: {
    type: Function,
    default: null,
  },
});

const editFieldMappingStore = useEditFieldMappingStore();
const dragStore = useEditFieldMappingDragStore();
const { showConfirmDialog, handleCancel, handleConfirm } = useConfirmDialog(editFieldMappingStore.hasUnsavedChanges);
const { route } = useRouter();

// Refs
const { mapping } = storeToRefs(editFieldMappingStore);
const {
  draggedElement,
  targetIndex,
  targetPanel,
  isFunctionDragging,
  isResourceFieldDragging,
  isResourceDropzoneFocused,
  showDialog,
  showFunctionDialog,
} = storeToRefs(dragStore);
const dropzonePlaceholder = ref([]);
const isTeleportEnabled = ref(false);
const selectedRows = ref<MappingFieldResponse[] | DynamicMappingFieldResponse[]>([]);
const selectedResourceRows = ref<MappingResourceFieldResponse[]>([]);
const fileUploader = ref<any>(null);
const uploadMappingModel = ref<File | null>(null);
const { isId } = useShowIdState();

// Computed
const isDynamicMapping = computed(() => route.path.includes('dynamicformats'));
const isOneRowSelected = computed(() => selectedRows.value?.length === 1);
const displayedResourceRows = computed(() =>
  !isDynamicMapping.value && mapping.value && (mapping.value as MappingDetailsResponse).ResourceFields
    ? (mapping.value as MappingDetailsResponse).ResourceFields
    : []
);
const isResourceDropzoneVisible = computed(
  () => !isDynamicMapping.value && (mapping.value as MappingDetailsResponse)?.ResourceFields?.length < 10
);
const selectedRowIsObjectType = computed(
  () =>
    isOneRowSelected.value &&
    editFieldMappingStore.isSelectedRowCustomType(selectedRows.value[0] as MappingFieldResponse)
);

// Computed property to get the correct row model for the dialog
const dialogRowModel = computed(() => {
  if (targetIndex.value === undefined || !mapping.value) {
    return null;
  }
  const { row } = getActualMappingRow(targetIndex.value);
  return row;
});

// Composables
const { t } = useI18n();
const {
  searchValue,
  allCategories,
  selectedCategory,
  allFieldTypes,
  selectedFieldTypes,
  allMapStates,
  selectedMapState,
  displayedRows,
} = useEditFieldMappingFilter(mapping);
const { onItemClick } = useLazySelectable(displayedRows as any, selectedRows as any, ref(0));
const { onItemClick: onResourceRowClick } = useLazySelectable(displayedResourceRows, selectedResourceRows, ref(0));
const { openDefaultLanguageDialog, setDefaultLanguage, showDefaultLanguageDialog, closeDefaultLanguageDialog } =
  useDefaultLanguageDialog();
const { isMapEnumButtonVisible, openMapEnumDialog, showMapEnumDialog } = useMapEnumDialogButtons(selectedRows);

// Functions
const changeMappingRows = (e) => {
  if (!e.added || !mapping.value || targetIndex.value === undefined) {
    return;
  }

  // Set the target panel type for this drag operation
  dragStore.setTargetPanel('standard');

  if (isFunctionDragging.value && draggedElement.value) {
    const functionRequiresSourceField = checkIfFunctionRequiresSourceField(
      (draggedElement.value as FunctionModel).Name as any
    );
    const targetRow = displayedRows.value[targetIndex.value];
    if ((functionRequiresSourceField && !!targetRow?.inRiverFieldTypeId) || !functionRequiresSourceField) {
      showFunctionDialog.value = true;
    } else {
      dragStore.clearStore();
    }
  } else {
    const targetRow = displayedRows.value[targetIndex.value];
    if (targetRow?.inRiverFieldTypeId) {
      showDialog.value = true;
    } else {
      mapElement();
    }
  }

  dropzonePlaceholder.value = [];
};

const changeResourceMappingRows = (e) => {
  if (isDynamicMapping.value) {
    return;
  }

  if (!isResourceFieldDragging.value) {
    notify.warning(t('core.settings.field_mapping.drop_wrong_field_type'));
  } else if (e.added && mapping.value) {
    (mapping.value as MappingDetailsResponse).ResourceFields.push({
      InRiverDataType: (e.added.element as InriverFieldType).dataType,
      InRiverFieldTypeId: (e.added.element as InriverFieldType).displayName,
      Id: uuidv4(),
    } as MappingResourceFieldResponse);
  }

  dropzonePlaceholder.value = [];
  draggedElement.value = undefined;
};

/**
 * Core mapping logic that applies field data to a mapping row without cleanup
 * @param mappingField gets modified with extra field info
 * @param field the field to map
 */
const applyFieldToMappingRow = (
  mappingField: DynamicMappingFieldResponse | MappingFieldResponse,
  field: InriverFieldType
) => {
  const sourceFieldData = {
    inRiverDataType: field.dataType,
    inRiverEntityTypeId: field.entityTypeId,
    inRiverFieldTypeId: field.id,
  };
  // TODO instead of Object.assign, we should use a more type-safe way to merge the data
  Object.assign(mappingField, sourceFieldData);
  const defaultMappingLanguage = editFieldMappingStore.getDefaultMappingLanguage();

  if (editFieldMappingStore.isLocaleStringField(mappingField as MappingFieldResponse) && defaultMappingLanguage) {
    editFieldMappingStore.linkChooseLanguageFunctionToField(
      mappingField as MappingFieldResponse,
      defaultMappingLanguage
    );
  }
};

/**
 * Puts the info from the dragged element into the selected row
 * @param mappingField gets modified with extra field info
 * @param field the dragged element
 */
const mapElementWithRow = (
  mappingField: DynamicMappingFieldResponse | MappingFieldResponse,
  field: InriverFieldType
) => {
  applyFieldToMappingRow(mappingField, field);

  dragStore.clearStore();
  selectedRows.value = [];
};

const mapElement = () => {
  if (!mapping.value || targetIndex.value === undefined || !draggedElement.value) {
    return;
  }

  const element = draggedElement.value as InriverFieldType;

  // Check if multiple rows are selected and the target is one of the selected rows
  const targetRow = displayedRows.value[targetIndex.value];
  const isTargetInSelection = selectedRows.value.some((selected) => {
    const fieldIdentifier = isDynamicMapping.value
      ? selected.FormatField
      : (selected as MappingFieldResponse).FormatFieldId;
    const targetIdentifier = isDynamicMapping.value
      ? targetRow.FormatField
      : (targetRow as MappingFieldResponse).FormatFieldId;
    return fieldIdentifier === targetIdentifier;
  });

  if (selectedRows.value.length > 1 && isTargetInSelection) {
    // Bulk mapping: map to all selected rows
    selectedRows.value.forEach((selectedRow) => {
      const fieldIdentifier = isDynamicMapping.value
        ? selectedRow.FormatField
        : (selectedRow as MappingFieldResponse).FormatFieldId;
      const actualRow = mapping.value?.MappingModelList.find((row) => {
        const rowIdentifier = isDynamicMapping.value ? row.FormatField : (row as MappingFieldResponse).FormatFieldId;
        return rowIdentifier === fieldIdentifier;
      });

      if (actualRow) {
        applyFieldToMappingRow(actualRow, element);
      }
    });

    // Clean up after bulk mapping
    dragStore.clearStore();
    selectedRows.value = [];
  } else {
    // Single mapping: map to the target row only
    const { row: actualRow } = getActualMappingRow(targetIndex.value);
    if (actualRow) {
      mapElementWithRow(actualRow, element);
    }
  }
};

const openFunctionSettings = () => {
  if (!selectedRows.value?.length) {
    return;
  }

  // Set the target panel type when opening function settings via click
  dragStore.setTargetPanel('standard');

  // Find the index in displayedRows (filtered rows) instead of the original mapping list
  // This ensures the dialog opens correctly even when search/filter is active
  const selectedRow = selectedRows.value[0];
  targetIndex.value = displayedRows.value.findIndex(filterMappingModelList(selectedRow));

  showFunctionDialog.value = true;
};

const importMappingIntoEditor = async () => {
  if (!mapping.value) {
    return;
  }
  fileUploader.value?.pickFiles();
};

const handleFiles = async (files) => {
  if (files.length > 1) {
    notify.error(t('core.settings.field_mapping.upload_mapping_file_length_error'), {
      position: 'bottom-right',
    });
    return;
  }

  const file = files[0];
  const fileExtension = file.name.split('.').pop().toLowerCase();
  if (fileExtension !== 'json') {
    notify.error(t('core.settings.field_mapping.upload_mapping_file_extension_error'), {
      position: 'bottom-right',
    });
    return;
  }

  const mappingToImport = await importMapping(file);
  const overrideFields = getCleanedUpMappingFields(mappingToImport as MappingDetailsResponse);
  const newMapping = copyTemplateWithOverrideMappings(mapping.value as MappingDetailsResponse, overrideFields);

  mapping.value = newMapping;
};

const exportCurrentMapping = async () => {
  if (!mapping.value) {
    return;
  }
  exportMapping(mapping.value);
};

const saveSettings = (settings: ConverterTransformation) => {
  // Only handle saves if this is the target panel or no panel is specified (for backward compatibility)
  if (targetPanel.value && targetPanel.value !== 'standard') {
    return;
  }

  if (!!settings && mapping.value && targetIndex.value !== undefined) {
    const { row: actualRow } = getActualMappingRow(targetIndex.value);
    if (actualRow) {
      actualRow.ConverterArgs = JSON.stringify({
        transformations: [settings],
      });

      if (draggedElement.value) {
        actualRow.ConverterId = (draggedElement.value as FunctionModel).Id;
      }
    }
  }

  dragStore.clearStore();
};

const filterMappingModelList = (selectedRow: MappingFieldResponse | DynamicMappingFieldResponse) => {
  const fieldName = isDynamicMapping.value ? 'FormatField' : 'FormatFieldId';

  return (x) => x[fieldName] === selectedRow[fieldName];
};

const unmapSourceField = () => {
  selectedRows.value?.forEach((selectedRow) => {
    const mappedRow = mapping.value?.MappingModelList.find(filterMappingModelList(selectedRow));
    if (!mappedRow) {
      return;
    }

    mappedRow.inRiverDataType = null;
    mappedRow.inRiverEntityTypeId = null;
    mappedRow.inRiverFieldTypeId = null;

    if (
      mappedRow.ConverterArgs &&
      checkIfFunctionRequiresSourceField(JSON.parse(mappedRow.ConverterArgs).transformations[0].function.name)
    ) {
      unmapFunction();
    }
  });

  selectedResourceRows.value?.forEach((selectedRow) => {
    const index = (mapping.value as MappingDetailsResponse)?.ResourceFields.findIndex(
      (field) => field.Id === selectedRow.Id
    );
    if (index > -1) {
      (mapping.value as MappingDetailsResponse)?.ResourceFields.splice(index, 1);
    }
  });
  selectedResourceRows.value = [];
};

const unmapFunction = () => {
  selectedRows.value?.forEach((selectedRow) => {
    const mappedRow = mapping.value?.MappingModelList.find(filterMappingModelList(selectedRow));
    if (!mappedRow) {
      return;
    }

    mappedRow.ConverterArgs = null;
    mappedRow.ConverterClass = null;
    mappedRow.ConverterId = null;
  });

  selectedResourceRows.value?.forEach((selectedRow) => {
    const mappedRow = (mapping.value as MappingDetailsResponse)?.ResourceFields.find(
      (field) => field.Id === selectedRow.Id
    );
    if (!mappedRow) {
      return;
    }

    mappedRow.ConverterArgs = null;
    mappedRow.ConverterId = null;
  });
};

const save = async () => {
  // Use the provided saveHandler if available, otherwise use the default save logic
  if (props.saveHandler) {
    const result = await props.saveHandler();
    selectedRows.value = [];
    return result;
  } else {
    // Default save logic for backward compatibility
    let result = false;
    if (isDynamicMapping.value) {
      result = await editFieldMappingStore.saveDynamicMappingChanges();
    } else {
      result = await editFieldMappingStore.saveChanges(props.mappingId);
    }

    result
      ? notify.success(t('core.settings.field_mapping.saved'))
      : notify.error(t('core.settings.field_mapping.warning'));
    selectedRows.value = [];
    return result;
  }
};

const confirm = () => {
  showDialog.value = false;
  if (!draggedElement.value) {
    return;
  }

  if (!!mapping.value && targetIndex.value !== undefined) {
    // Check if multiple rows are selected and the target is one of the selected rows
    const targetRow = displayedRows.value[targetIndex.value];
    const isTargetInSelection = selectedRows.value.some((selected) => {
      const fieldIdentifier = isDynamicMapping.value
        ? selected.FormatField
        : (selected as MappingFieldResponse).FormatFieldId;
      const targetIdentifier = isDynamicMapping.value
        ? targetRow.FormatField
        : (targetRow as MappingFieldResponse).FormatFieldId;
      return fieldIdentifier === targetIdentifier;
    });

    if (selectedRows.value.length > 1 && isTargetInSelection) {
      // Clear converter args for all selected rows
      selectedRows.value.forEach((selectedRow) => {
        const fieldIdentifier = isDynamicMapping.value
          ? selectedRow.FormatField
          : (selectedRow as MappingFieldResponse).FormatFieldId;
        const actualRow = mapping.value?.MappingModelList.find((row) => {
          const rowIdentifier = isDynamicMapping.value ? row.FormatField : (row as MappingFieldResponse).FormatFieldId;
          return rowIdentifier === fieldIdentifier;
        });

        if (actualRow) {
          actualRow.ConverterArgs = null;
          actualRow.ConverterClass = null;
          actualRow.ConverterId = null;
        }
      });
    } else {
      // Clear converter args for the target row only
      const { row: actualRow } = getActualMappingRow(targetIndex.value);
      if (actualRow) {
        actualRow.ConverterArgs = null;
        actualRow.ConverterClass = null;
        actualRow.ConverterId = null;
      }
    }
  }

  mapElement();
};

const cancel = () => {
  showDialog.value = false;
  dragStore.clearStore();
};

const cancelEdit = () => {
  emit('cancel-edit');
};

const isEnumField = (row: MappingFieldResponse): boolean => {
  return (
    !!row.Enumerations &&
    (row.FormatDataType.toUpperCase().trim() === RowTargetType.ENUM.toUpperCase() ||
      row.FormatDataType.toUpperCase().trim() === RowTargetType.LIST_ENUM.toUpperCase())
  );
};

// Button configuration using our composable - moved after function declarations
const buttonConfig = useFieldMappingButtons({
  panelType: MappingPanelType.Standard,
  selectedRows,
  selectedResourceRows,
  displayedRows,
  isDynamicMapping,
  selectedRowIsObjectType,
  // Handler functions
  saveHandler: save,
  cancelEditHandler: cancelEdit,
  mapAutomaticallyHandler: () => editFieldMappingStore.mapAutomatically(),
  unmapSourceFieldHandler: unmapSourceField,
  unmapFunctionHandler: unmapFunction,
  openDefaultLanguageDialogHandler: openDefaultLanguageDialog,
  importMappingHandler: importMappingIntoEditor,
  exportMappingHandler: exportCurrentMapping,
  openFunctionSettingsHandler: openFunctionSettings,
  openMapEnumDialogHandler: openMapEnumDialog,
  addListItemHandler: () => editFieldMappingStore.addListItem(selectedRows.value[0]),
  removeListItemHandler: () => editFieldMappingStore.removeListItem(selectedRows.value[0]),
  // Visibility functions
  isMapEnumButtonVisibleFn: isMapEnumButtonVisible,
});

// Helper function to get the actual mapping row from the filtered displayedRows
const getActualMappingRow = (filteredIndex: number) => {
  if (!mapping.value || filteredIndex === undefined || filteredIndex < 0) {
    return { row: null, actualIndex: -1 };
  }

  const targetRow = displayedRows.value[filteredIndex];
  if (!targetRow) {
    return { row: null, actualIndex: -1 };
  }

  // Find the actual row in the mapping using the field identifier
  const fieldIdentifier = isDynamicMapping.value ? targetRow.FormatField : targetRow.FormatFieldId;
  const actualIndex = mapping.value.MappingModelList.findIndex((row) => {
    const rowIdentifier = isDynamicMapping.value ? row.FormatField : (row as MappingFieldResponse).FormatFieldId;
    return rowIdentifier === fieldIdentifier;
  });

  return {
    row: actualIndex !== -1 ? mapping.value.MappingModelList[actualIndex] : null,
    actualIndex,
  };
};

// Helper function to get the actual mapping index from filtered displayedRows index
const getActualMappingIndex = (filteredIndex: number): number => {
  const { actualIndex } = getActualMappingRow(filteredIndex);
  return actualIndex;
};

// Lifecycle methods
onMounted(() => {
  isTeleportEnabled.value = true;
});
</script>

<style lang="scss" scoped>
.editor {
  max-height: calc(100vh - 200px);
  position: relative;
  overflow: auto;
}

.edit-field-mapping-table {
  padding: 10px 0px;
}

.edit-field-mapping-resources {
  padding: 10px 0px;
}

.dropzone {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  * {
    opacity: 0;
  }
}

:deep(.sticky-table-header) {
  thead tr th {
    position: sticky;
    z-index: 4;
  }
}

.resource-dropzone {
  width: 100%;
  height: 31px;
  border: 1px dashed var(--color-grey);
  margin: 10px 0px;
}

.focused {
  border: 1px dashed var(--color-green) !important;

  .target {
    background-color: var(--color-green-light-30) !important;
  }
}

.resource-focused {
  border: 1px dashed var(--color-green) !important;
  background-color: var(--color-green-light-30) !important;
}

.mapped {
  border: 1px black solid;
}

.changed {
  background-color: var(--color-yellow-light);
}

.unmapped {
  .source {
    background-color: var(--color-red-light);
  }

  .target {
    background-color: var(--color-grey-10);
  }
}

.label {
  width: 31px;
  height: 31px;

  &.mandatory {
    background-color: var(--color-orange);
  }

  &.recommended {
    background-color: var(--color-blue-light);
  }

  &.unique {
    background-color: var(--color-yellow);
  }
}

.square {
  z-index: 3;
  position: relative;
}

:deep(.c-inri-input.c-inri-custom-search) {
  &.q-field--outlined .q-field__control {
    padding: 0px;
    border-radius: 0px;
    background: var(--color-grey-lighter);
    margin-bottom: 10px;

    &::after {
      border: 0 !important;
    }
  }

  &.c-inri-input--dense.q-field--outlined .q-field__control::before {
    border: 0 !important;
  }

  .q-field__inner .q-field__control {
    padding: 0px;
    padding-left: 10px;
    background: var(--color-grey-lighter);
    border-radius: 0px;

    .q-field__append span {
      width: 40px;
      height: 40px;
      border-left: 1px solid var(--surface-color);

      svg {
        height: 50%;
        width: 50%;
      }
    }
  }
}
</style>
