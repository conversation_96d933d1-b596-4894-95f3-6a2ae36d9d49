import {
  ApiEnabledTradingPartner,
  ApiTradingPartner,
  ApiTradingPartnerCategoriesResponse,
  ApiTradingPartnerCategory,
  ApiTradingPartnerResponse,
} from '@core/interfaces/ApiTradingPartner';
import { outputAdapterClient } from '@core/Utils';
import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';

export const fetchTradingPartnersFromApi = async (): Promise<ApiTradingPartner[]> => {
  const url = `/api/trading-partners`;
  const response = await outputAdapterClient.get<ApiTradingPartnerResponse>(url, 'Error fetching trading partners');
  return response.data?.tradingPartners ?? [];
};

export const fetchEnabledTradingPartnersFromApi = async (): Promise<ApiEnabledTradingPartner[]> => {
  const environmentGid = getEnvironmentGlobalId();
  const url = `/api/environments/${environmentGid}/trading-partners`;
  const response = await outputAdapterClient.get<ApiTradingPartnerResponse>(url, 'Error fetching trading partners');
  return response.data?.items ?? [];
};

export const fetchTradingPartnerCategoriesApi = async (
  tradingPartnerId: string
): Promise<ApiTradingPartnerCategory[]> => {
  const url = `/api/trading-partners/${tradingPartnerId}/categories`;
  const response = await outputAdapterClient.get<ApiTradingPartnerCategoriesResponse>(
    url,
    'Error fetching trading partner categories'
  );
  return response.data?.categories ?? [];
};
