<template>
  <teleport v-if="isTeleportEnabled" to="#right-sidebar">
    <c-drop-down-menu v-if="isAddApiButtonEnabled" data-test-id="settings-menu" icon="mdi-plus-circle-outline">
      <template #items>
        <c-drop-down-menu-item
          :text="$t('core.manage_trading_partners.buttons.add_api')"
          data-test-id="add-api"
          @click="showAddApiDialog = true"
        />
        <c-drop-down-menu-item
          :text="$t('core.manage_trading_partners.buttons.add_flatfile')"
          data-test-id="upload-format-file"
          @click="showCreateFormatFileDialog = true"
        />
      </template>
    </c-drop-down-menu>
    <c-tile-btn
      v-if="!isAddApiButtonEnabled"
      icon="mdi-plus-circle-outline"
      :tooltip-left="$t('core.manage_trading_partners.buttons.add_flatfile')"
      data-test-id="upload-format-file"
      :icon-size="20"
      @click.stop="showCreateFormatFileDialog = true"
    />
    <c-tile-btn
      v-if="isDetailsButtonVisible"
      icon="mdi-ray-start-arrow"
      :tooltip-left="$t('core.manage_trading_partners.manage_mappings')"
      :icon-size="20"
      @click.stop="goToViewMappings"
    />
    <c-tile-btn
      v-if="isRowSelected"
      icon="mdi-format-list-group"
      :tooltip-left="$t('core.manage_trading_partners.assign_collections')"
      :icon-size="20"
      @click.stop="goToAssignCollectionsPage"
    />
    <c-tile-btn
      v-if="isRowSelected"
      icon="mdi-send-outline"
      :tooltip-left="$t('core.manage_trading_partners.assign_outputs')"
      :icon-size="20"
      @click.stop="goToAssignOutputsPage"
    />
    <c-tile-btn
      v-if="isDownloadButtonVisible"
      icon="mdi-download-outline"
      :icon-size="20"
      :tooltip-left="$t('core.manage_trading_partners.download')"
      @click.stop="downloadSelectedTemplate"
    />
    <c-tile-btn
      v-if="isDeleteButtonVisible"
      :tooltip-left="$t('core.manage_trading_partners.delete')"
      icon="mdi-delete-outline"
      :icon-size="20"
      @click.stop="showConfirmDialog = true"
    />
  </teleport>
  <q-inner-loading :showing="shouldShowSpinner" color="primary" class="inner-loading">
    <c-spinner color="primary" size="40" />
  </q-inner-loading>
  <q-table
    v-if="!!displayedTradingPartners.length"
    v-model:selected="selectedRows"
    class="trading-partners-table sticky-table-header"
    :pagination="{
      page: 1,
      rowsPerPage: 0,
      sortBy: 'Name',
      descending: false,
    }"
    flat
    dense
    hide-bottom
    separator="cell"
    binary-state-sort
    :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
    :columns="ManageTradingPartnerColumns"
    :hide-header="false"
    :rows="displayedTradingPartners"
    :row-key="getRowKey"
    :rows-per-page-options="[0]"
    @row-click="onRowClick"
    @row-dblclick="(_, row) => onRowDoubleClick(_, row, goToViewMappings)"
  >
    <template #body-cell-type="props">
      <q-td :props="props"> {{ props.row.type || FormatType.FILE }}</q-td>
    </template>
  </q-table>
  <c-no-data
    v-if="showNoData"
    src="nothing-to-see"
    image-height="195px"
    :title="$t('core.manage_trading_partners.no_formats_title')"
    :text="$t('core.manage_trading_partners.no_formats_message')"
  />
  <c-confirm-dialog
    v-if="showConfirmDialog"
    v-model:show="showConfirmDialog"
    :title="$t('core.manage_trading_partners.confirm_delete.title')"
    :text="
      $t('core.manage_trading_partners.confirm_delete.text', {
        tradingPartner: selectedRows ? selectedRows[0].Name : '',
      })
    "
    :confirm-button-text="$t('core.manage_trading_partners.delete')"
    @handle-confirm="onDelete"
    @handle-cancel="showConfirmDialog = false"
  />
  <c-create-format-file-dialog
    v-if="showCreateFormatFileDialog"
    v-model:show="showCreateFormatFileDialog"
    @format-file-created="fetchFormats"
  />
  <c-add-api-trading-partner-dialog
    v-if="showAddApiDialog"
    v-model:show="showAddApiDialog"
    @format-created="fetchFormats"
  />
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { useRouter } from '@composables/useRouter';
import { FormatFileResponse, DynamicFormatFile } from '@core/interfaces';
import { useSingleRowSelect } from '@composables/Common';
import { useManageTradingPartners } from '@core/composables/TradingPartners';
import { ManageTradingPartnerColumns } from '@core/const';
import { useCoreTradingPartnersStore } from '@core/stores';
import { CNoData, CConfirmDialog } from '@components';
import { CDropDownMenu, CDropDownMenuItem } from '@core/components/Common';
import { CCreateFormatFileDialog, CAddApiTradingPartnerDialog } from '@core/components/TradingPartners';
import { FormatType } from '@core/enums';
import isFeatureEnabled from '@utils/isFeatureEnabled';
import accessService from '@services/AccessService';
import { useEnabledApiStore } from '@core/stores/output-adapter/useEnabledApiStore';

// Stores
const coreTradingPartnersStore = useCoreTradingPartnersStore();

// Refs
const showCreateFormatFileDialog = ref<boolean>(false);
const showAddApiDialog = ref<boolean>(false);
const showConfirmDialog = ref(false);
const isTeleportEnabled = ref(false);
const { isLoading: isFetching, coreFormats, showNoData, dynamicFormats } = storeToRefs(coreTradingPartnersStore);

// Computed
const isAddApiButtonEnabled = computed(
  () =>
    isFeatureEnabled('not-ready') ||
    (accessService.hasPermission('SyndicateAdvance_Config') && enabledApis?.value?.length > 0)
);
const isRowSelected = computed(() => !!selectedRows.value?.length);
const isDownloadButtonVisible = computed(() => isRowSelected.value);
const isDetailsButtonVisible = computed(() => isRowSelected.value);
const isDeleteButtonVisible = computed(() => isRowSelected.value);
const shouldShowSpinner = computed(
  () => !hasFormats.value && (isFetching.value || isManageTradingPartnersLoading.value)
);
const hasFormats = computed(() => !!coreFormats.value?.length || !!dynamicFormats.value?.length);
const displayedTradingPartners = computed(() =>
  [...coreFormats.value, ...dynamicFormats.value].sort((a, b) => {
    return a.Name?.localeCompare(b.Name);
  })
);
const isSelectedFormatDynamic = computed(() => {
  if (isRowSelected.value) {
    return 'type' in selectedRows.value[0] && selectedRows.value[0].type === FormatType.API;
  }

  return false;
});

// Composables
const { t } = useI18n();
const enabledApiStore = useEnabledApiStore();
const { enabledApis } = storeToRefs(enabledApiStore);
const { goToPage } = useRouter();
const { selectedRows, onRowClick, onRowDoubleClick } = useSingleRowSelect<FormatFileResponse>();
const {
  isLoading: isManageTradingPartnersLoading,
  downloadSelectedTemplate,
  deleteSelectedTradingPartner,
} = useManageTradingPartners(selectedRows);

// Functions
const goToViewMappings = () => {
  if (isSelectedFormatDynamic.value) {
    goToPage('dynamic-format-mappings-page', { formatId: selectedRows.value[0].Id });
    return;
  }

  goToPage('format-mappings-page', {
    formatId: selectedRows.value[0].Id,
  });
};

const goToAssignCollectionsPage = () => {
  if (isSelectedFormatDynamic.value) {
    goToPage('assign-collections', null, {
      formatIds: selectedRows.value?.filter((x) => 'type' in x && x.type === FormatType.API).map((x) => x.Id),
    });
    return;
  }

  goToPage('assign-collections-to-file-format', null, {
    formatIds: selectedRows.value?.filter((x) => x.type !== FormatType.API).map((x) => x.Id),
  });
};

const goToAssignOutputsPage = () => {
  const query = {
    formatIds: selectedRows.value.map((x) => x.Id),
  };
  const routeName = isSelectedFormatDynamic.value ? 'assign-dynamic-outputs' : 'assign-core-outputs';

  goToPage(routeName, null, query);
};

const onDelete = async () => {
  const success = await deleteSelectedTradingPartner(isSelectedFormatDynamic.value);
  if (success) {
    await fetchFormats();
    notify.success(t('core.manage_trading_partners.delete_format_file_success'));
    selectedRows.value = [];
  } else {
    notify.error(t('core.manage_trading_partners.delete_format_file_error'));
  }
};

const fetchFormats = async () => {
  await coreTradingPartnersStore.fetchFormats();
};

const getRowKey = (row: FormatFileResponse | DynamicFormatFile) =>
  'type' in row ? row.Id + row.type : (row as FormatFileResponse).Id + FormatType.FILE;

// Lifecycle methods
onBeforeMount(async () => {
  // run requests in parallel
  const fetchTradingPartners = fetchFormats();
  const fetchApis = enabledApiStore.fetchEnabledApis();
  await Promise.all([fetchTradingPartners, fetchApis]);
});

onMounted(async () => {
  isTeleportEnabled.value = true;
});
</script>

<style lang="scss" scoped>
.inner-loading {
  z-index: 3;
}

.trading-partners-table {
  max-height: calc(100vh - 190px);
  margin-bottom: 200px;
}
</style>
