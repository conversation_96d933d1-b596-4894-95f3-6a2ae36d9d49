import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { WelcomeSettingsPage } from '@pages/welcomePage/welcomeSettings/welcomeSettings.page';
import { FormatsTabPage } from '@pages/welcomePage/welcomeSettings/formatsTab/formatsTab.page';

test.describe('Formats tab', () => {
  const formatAccordions = 'Accordions';
  const tradingPartnerSafeName = 'best-buy';
  const tradingPartnerName = 'Best Buy';
  const category = 'Accordions';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let welcomeSettingsPage: WelcomeSettingsPage;
  let formatTabPage: FormatsTabPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    welcomeSettingsPage = new WelcomeSettingsPage(localPage);
    formatTabPage = new FormatsTabPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.settingsButton.click();
    await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();
    await welcomeSettingsPage.formatsTab.click();
    await expect(formatTabPage.formatsTable, 'Formats Table is not visible').toBeVisible();
  });

  test('Add and delete format api', async () => {
    // Remove format if exists
    const existingFormat = await formatTabPage
      .selectFormat(`${tradingPartnerSafeName} - ${formatAccordions}`)
      .isVisible();
    if (existingFormat) {
      await formatTabPage.deleteFormat(`${tradingPartnerSafeName} - ${formatAccordions}`);
    }

    // Create new format
    await expect(async () => {
      await formatTabPage.addFormatButton.hover();
      await formatTabPage.addApiButton.click();
      await expect(formatTabPage.createButton).toBeEnabled();
      await expect(formatTabPage.dialogSpinner).toBeHidden();
    }).toPass();
    await formatTabPage.tradingPartnerDropdown.click();
    await formatTabPage.selectTradingPartnerDropdownItem(tradingPartnerName).click();
    await formatTabPage.categoryDropdown.click();
    await formatTabPage.selectCategoryDropdownItem(category).click();
    await formatTabPage.createButton.click();
    await formatTabPage.successNotification.waitFor({ state: 'visible' });
    await formatTabPage.successNotification.waitFor({ state: 'hidden' });
    await expect(
      formatTabPage.selectFormat(`${tradingPartnerSafeName} - ${formatAccordions}`),
      'Format is not visible'
    ).toBeVisible();

    // Delete format
    await formatTabPage.deleteFormat(`${tradingPartnerSafeName} - ${formatAccordions}`);
    await expect(formatTabPage.selectFormat(`${tradingPartnerSafeName} - ${formatAccordions}`)).not.toBeVisible();
  });

  test('Add and delete format file, verify table still shows other formats @notForPr', async () => {
    const testCompanyName1 = 'Test Company 1';
    const testCategoryName1 = 'Test Category 1';
    const expectedFormatName1 = `${testCompanyName1} - ${testCategoryName1}`;
    const testCompanyName2 = 'Test Company 2';
    const testCategoryName2 = 'Test Category 2';
    const expectedFormatName2 = `${testCompanyName2} - ${testCategoryName2}`;

    const existingTestFormat1 = await formatTabPage.selectFormat(expectedFormatName1).isVisible();
    if (existingTestFormat1) {
      await formatTabPage.deleteFormat(expectedFormatName1);
    }
    const existingTestFormat2 = await formatTabPage.selectFormat(expectedFormatName2).isVisible();
    if (existingTestFormat2) {
      await formatTabPage.deleteFormat(expectedFormatName2);
    }

    // Create new format file 1
    await expect(async () => {
      await formatTabPage.addFormatButton.hover();
      await formatTabPage.uploadFormatButton.click();
      await expect(formatTabPage.createButton).toBeDisabled();
      await expect(formatTabPage.dialogSpinner).toBeHidden();
    }).toPass();
    await formatTabPage.uploadFormatFile(testCompanyName1, testCategoryName1);
    await formatTabPage.successNotification.waitFor({ state: 'visible' });
    await formatTabPage.successNotification.waitFor({ state: 'hidden' });
    await expect(formatTabPage.selectFormat(expectedFormatName1), 'Uploaded format is not visible').toBeVisible();

    // Create new format file 2
    await expect(async () => {
      await formatTabPage.addFormatButton.hover();
      await formatTabPage.uploadFormatButton.click();
      await expect(formatTabPage.createButton).toBeDisabled();
      await expect(formatTabPage.dialogSpinner).toBeHidden();
    }).toPass();
    await formatTabPage.uploadFormatFile(testCompanyName2, testCategoryName2);
    await formatTabPage.successNotification.waitFor({ state: 'visible' });
    await formatTabPage.successNotification.waitFor({ state: 'hidden' });
    await expect(formatTabPage.selectFormat(expectedFormatName2), 'Uploaded format is not visible').toBeVisible();

    // Delete format file 1, check the second one is still visible
    await formatTabPage.deleteFormat(expectedFormatName1);
    await expect(formatTabPage.selectFormat(expectedFormatName1)).not.toBeVisible();
    await expect(formatTabPage.selectFormat(expectedFormatName2)).toBeVisible();

    await formatTabPage.deleteFormat(expectedFormatName2);
    await expect(formatTabPage.selectFormat(expectedFormatName2)).not.toBeVisible();
  });
});
