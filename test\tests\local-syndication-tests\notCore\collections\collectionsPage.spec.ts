import { test, expect } from '@fixtures/localPageFixture';
import { ProductTabPage } from '@pages/notCore/product/productTab.page';
import { CollectionsTabPage } from '@pages/notCore/collections/collectionsTab.page';
import { SyndicationPage } from '@pages/notCore/syndication.page';

test.describe('Collections page', () => {
  const defaultRetailerName = 'DSW';
  const collectionName = 'Collection auto test';
  const renameCollectionName = 'Rename collection auto test';

  let productTabPage: ProductTabPage;
  let collectionTabPage: CollectionsTabPage;
  let syndicationPage: SyndicationPage;

  test.beforeAll(async ({ localPage }) => {
    productTabPage = new ProductTabPage(localPage);
    collectionTabPage = new CollectionsTabPage(localPage);
    syndicationPage = new SyndicationPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await localPage.getByText(defaultRetailerName).click();
    await syndicationPage.collectionsTab.click();

    // Wait for either the table or the no-data component to be visible
    await Promise.race([
      collectionTabPage.collectionTable.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {
        /* ignore timeout error */
        return false;
      }),
      collectionTabPage.noDataComponent.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {
        /* ignore timeout error */
        return false;
      }),
    ]);

    const hasCollections = await collectionTabPage.hasCollections();
    if (!hasCollections) {
      return;
    }

    const collectionExists = await collectionTabPage
      .selectCollection(collectionName)
      .isVisible()
      .catch(() => false);

    if (collectionExists) {
      await collectionTabPage.deleteCollection(collectionName);
      await expect(collectionTabPage.selectCollection(collectionName)).toBeHidden({ timeout: 1000 });
    }

    // Check for the renamed collection name
    const renamedCollectionExists = await collectionTabPage
      .selectCollection(renameCollectionName)
      .isVisible()
      .catch(() => false);

    if (renamedCollectionExists) {
      await collectionTabPage.deleteCollection(renameCollectionName);
      await expect(collectionTabPage.selectCollection(renameCollectionName)).toBeHidden({ timeout: 1000 });
    }
  });

  test('Create collection and add products @notForPr', async () => {
    await collectionTabPage.createCollection(collectionName);
    await syndicationPage.productsTab.click();
    await expect
      .soft(productTabPage.addToCollectionButton, 'Collection button should be hidden before selecting any product')
      .toBeHidden();
    const productDescription = await productTabPage.productCard.nth(0).locator('.description').textContent();
    await productTabPage.productCard.nth(0).click();
    await productTabPage.addToCollectionButton.click();
    await productTabPage.selectCollection(collectionName);
    await productTabPage.addCollectionButton.click();
    await syndicationPage.collectionsTab.click();
    await collectionTabPage.selectCollection(collectionName).click();
    await collectionTabPage.openViewCollection();
    await expect
      .poll(async () => {
        const result = await productTabPage.getRowCountForTableContainingText(productDescription);
        return result > 0;
      }, 'Product was not added to the collection')
      .toBeTruthy();
  });

  test('Rename collection @notForPr', async () => {
    await collectionTabPage.createCollection(collectionName);
    await collectionTabPage.selectCollection(collectionName).click();

    await expect(collectionTabPage.renameCollectionButton, 'Rename collection button is NOT VISIBLE').toBeVisible();

    await collectionTabPage.renameCollectionButton.click();
    await collectionTabPage.renameCollection(renameCollectionName);

    await expect(
      collectionTabPage.renamedCollection(renameCollectionName),
      'Renamed collection should be visible'
    ).toBeVisible();
  });
});
