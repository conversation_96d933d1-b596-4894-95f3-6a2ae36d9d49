export interface ApiTradingPartner {
  id: string;
  name: string;
  logoUrl: string;
}

export interface ApiEnabledTradingPartner {
  environmentGid: string;
  isAvailableToAll: boolean;
  isTestMode: boolean;
  tradingPartnerId: string;
  tradingPartnerName: string;
}

export interface ApiTradingPartnerCategory {
  id: string;
  name: string;
}

export interface ApiTradingPartnerResponse {
  tradingPartners: ApiTradingPartner[];
}

export interface ApiEnabledTradingPartnerResponse {
  items: ApiEnabledTradingPartner[];
}

export interface ApiTradingPartnerCategoriesResponse {
  categories: ApiTradingPartner[];
}
