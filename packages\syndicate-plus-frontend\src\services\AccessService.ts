import featureService from '@services/FeatureService';
import permissionService from '@services/PermissionService';
import AccessHttpService from '@httpservices/AccessHttpService';
import isFeatureEnabled from '@utils/isFeatureEnabled';

class AccessService {
  hasSyndicatePlusAdminAccess() {
    const hasPermission = this.hasPermission('SyndicatePlusAdmin');
    return featureService.isSyndicatePlusAdminEnabled() && hasPermission;
  }

  hasPermission(permission: string): boolean {
    const hasPermission = permissionService.getPermissions().find((i) => i == permission) !== undefined;
    if (hasPermission) {
      return true;
    }
    // if running local development, check localStorage for a specific key
    if (import.meta.env.DEV) {
      const localPermissions = localStorage.getItem('syndicate-advance.setting.permissions')?.split(',') ?? [];
      return localPermissions.includes(permission);
    }
    return false;
  }

  async hasSyndicateAplusAccess(orgId: number): Promise<boolean> {
    return await AccessHttpService.hasSyndicateAplusAccess(orgId);
  }

  hasSyndicateAdvanceConfigAccess() {
    return permissionService.getPermissions().find((i) => i == 'SyndicateAdvance_Config') !== undefined;
  }

  hasSyndicatePlusV2Access() {
    return (
      featureService.isSyndicatePlusV2Enabled() ||
      permissionService.getPermissions().find((i) => i == 'SyndicatePlusV2') !== undefined ||
      isFeatureEnabled('plus-v2-enabled')
    );
  }
}

export default new AccessService();
