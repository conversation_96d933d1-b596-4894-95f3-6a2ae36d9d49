import { Locator, Page } from '@playwright/test';
import { BasePage } from '@pages/basePage.page';

export class CollectionsTabPage extends BasePage {
  private page: Page;
  readonly collectionTable: Locator;
  readonly noDataComponent: Locator;
  readonly createNewCollectionButton: Locator;
  readonly createCollectionNameField: Locator;
  readonly createButton: Locator;
  readonly viewCollectionButton: Locator;
  readonly renameCollectionButton: Locator;
  readonly renameCollectionNameField: Locator;
  readonly saveButton: Locator;
  readonly exportMenuButton: Locator;
  readonly exportMediaAsZipButton: Locator;
  readonly deleteCollectionButton: Locator;
  readonly saveToOutboxButton: Locator;
  readonly closeCollectionDialog: Locator;
  readonly confirmDeleteButton: Locator;

  readonly selectCollection = (text: string): Locator =>
    this.page.getByRole('cell', { name: text, exact: true }).nth(0);
  readonly renamedCollection = (text: string): Locator => this.page.getByRole('cell', { name: text });

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.collectionTable = page.locator('.collections-table');
    this.noDataComponent = page.locator('.no-data-table');
    this.createNewCollectionButton = page.getByLabel('create new collection', { exact: true });
    this.createCollectionNameField = page.getByLabel('collection name');
    this.createButton = page.getByRole('button', { name: 'create', exact: true });
    this.viewCollectionButton = page.getByTestId('view-collection-details-button');
    this.renameCollectionButton = page.getByLabel('rename collection');
    this.renameCollectionNameField = page.getByLabel('collection name');
    this.saveButton = page.getByRole('button', { name: 'save', exact: true });
    this.exportMenuButton = page.getByTestId('export_button');
    this.exportMediaAsZipButton = page.getByLabel('export media as zip');
    this.deleteCollectionButton = page.getByLabel('delete collection', { exact: true });
    this.saveToOutboxButton = page.getByLabel('outbox');
    this.closeCollectionDialog = page.getByLabel('close');
    this.confirmDeleteButton = page.locator('button').filter({ hasText: 'delete' });
  }

  async createCollection(text: string): Promise<void> {
    await this.createNewCollectionButton.click();
    await this.createCollectionNameField.fill(text);
    await this.createButton.click();
  }

  async openCollection(text: string): Promise<void> {
    await this.selectCollection(text).click();
  }

  async deleteCollection(text: string): Promise<void> {
    await this.selectCollection(text).click();
    await this.deleteCollectionButton.click();
    await this.confirmDeleteButton.click();
  }

  async renameCollection(newName: string): Promise<void> {
    await this.renameCollectionNameField.clear();
    await this.renameCollectionNameField.fill(newName);
    await this.saveButton.click();
  }

  async openViewCollection(): Promise<void> {
    await this.viewCollectionButton.click();
  }

  async hasCollections(): Promise<boolean> {
    const isTableVisible = await this.collectionTable.isVisible().catch(() => false);
    const isNoDataVisible = await this.noDataComponent.isVisible().catch(() => false);

    return isTableVisible && !isNoDataVisible;
  }
}
